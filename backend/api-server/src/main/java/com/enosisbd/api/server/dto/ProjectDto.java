package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Email;
// Import other constraints if needed, e.g., URL, but basic validation is often enough here
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ProjectDto extends BaseDto {
    @NotNull(message = "Project name cannot be null")
    @Size(min = 3, max = 255, message = "Project name must be between 3 and 255 characters")
    private String name;

    private String description;

    // Google Sheets metadata
    /**
     * The ID of the Google Sheet associated with this project.
     * This is extracted from the Google Sheet URL and used for API access.
     */
    private String googleSheetId;

    /**
     * The URL of the Google Sheet associated with this project.
     */
    private String googleSheetUrl;

    /**
     * The column letter (e.g., "A", "B") that contains submodule names in the Google Sheet.
     */
    private String submoduleColumn;

    /**
     * The row number where submodule data starts in the Google Sheet (1-based).
     */
    private Integer submoduleStartRow;

    /**
     * The email of the user who has access to the Google Sheet.
     * This is necessary for Google OAuth authentication when accessing the sheet.
     * The system uses this email to verify permissions and access the sheet's data
     * through the Google Sheets API.
     */
    private String googleUserEmail;

    /**
     * The column letter (e.g., "A", "B") that contains case IDs in the Google Sheet.
     */
    private String caseIdColumn;

    /**
     * The column letter (e.g., "A", "B") that contains test case descriptions in the Google Sheet.
     */
    private String testCaseDescriptionColumn;

    /**
     * The column letter (e.g., "A", "B") that contains test case expected results in the Google Sheet.
     */
    private String testCaseExpectedResultColumn;
}
