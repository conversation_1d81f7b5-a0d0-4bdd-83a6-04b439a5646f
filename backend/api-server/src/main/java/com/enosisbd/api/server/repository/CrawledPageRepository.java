package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.CrawledPage;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CrawledPageRepository extends BaseRepository<CrawledPage, Long> {
    @Query("from CrawledPage cp left join fetch cp.project where cp.id = :id")
    Optional<CrawledPage> findByIdJoined(Long id);

    @Query("from CrawledPage cp left join fetch cp.project p where p.id = :projectId")
    List<CrawledPage> findByProjectIdJoined(Long projectId);

    @Query("from CrawledPage cp where cp.project.id = :projectId")
    List<CrawledPage> findByProjectId(Long projectId);

    @Query("from CrawledPage cp where cp.id in :ids")
    List<CrawledPage> findByIds(List<Long> ids);
}